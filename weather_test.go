package sdk_weather

import (
	"context"
	"encoding/base64"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"ccc-gitlab.leihuo.netease.com/cccgo/kit"
	"github.com/go-resty/resty/v2"
)

func TestNewMissingHost(t *testing.T) {
	conf := Config{AppID: "appid", Secret: "secret"}
	_, err := New(conf)
	if err == nil {
		t.Fatalf("expected error when host is missing")
	}
	if !errors.Is(err, ErrWeatherSDKInitErr) {
		t.Fatalf("expected ErrWeatherSDKInitErr, got %v", err)
	}
}

func TestNewMissingAppID(t *testing.T) {
	conf := Config{Host: "http://example.com", Secret: "secret"}
	_, err := New(conf)
	if err == nil {
		t.Fatalf("expected error when appid is missing")
	}
	if !errors.Is(err, ErrWeatherSDKInitErr) {
		t.Fatalf("expected ErrWeatherSDKInitErr, got %v", err)
	}
}

func TestNewMissingSecret(t *testing.T) {
	conf := Config{Host: "http://example.com", AppID: "appid"}
	_, err := New(conf)
	if err == nil {
		t.Fatalf("expected error when secret is missing")
	}
	if !errors.Is(err, ErrWeatherSDKInitErr) {
		t.Fatalf("expected ErrWeatherSDKInitErr, got %v", err)
	}
}

func TestGetBasicAuthToken(t *testing.T) {
	weather := &Weather{conf: Config{AppID: "appid", Secret: "secret"}}
	token := weather.getBasicAuthToken()
	expected := "Basic " + base64.StdEncoding.EncodeToString([]byte("appid:secret"))
	if token != expected {
		t.Fatalf("expected auth token %s, got %s", expected, token)
	}
}

func TestGetWeatherByLocationSuccess(t *testing.T) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != weatherAPIPath {
			t.Fatalf("unexpected path: %s", r.URL.Path)
		}
		q := r.URL.Query()
		if got := q.Get("gameid"); got != "game" {
			t.Fatalf("expected gameid=game, got %s", got)
		}
		if got := q.Get("lat"); got != "12.345600" {
			t.Fatalf("expected lat=12.345600, got %s", got)
		}
		if got := q.Get("log"); got != "34.567800" {
			t.Fatalf("expected log=34.567800, got %s", got)
		}
		if got := q.Get("station"); got != "station-1" {
			t.Fatalf("expected station=station-1, got %s", got)
		}
		if got := q.Get("adcode"); got != "adcode-1" {
			t.Fatalf("expected adcode=adcode-1, got %s", got)
		}

		expectedAuth := "Basic " + base64.StdEncoding.EncodeToString([]byte("appid:secret"))
		if auth := r.Header.Get("Authorization"); auth != expectedAuth {
			t.Fatalf("expected Authorization header %s, got %s", expectedAuth, auth)
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(`{
            "code": 0,
            "result": {
                "weather": {
                    "humidity": "60",
                    "reporttime": "2024-01-01T00:00:00Z",
                    "temperature": "20",
                    "weather": "Sunny",
                    "weather_code": 1,
                    "winddirection": "N",
                    "windpower": "3"
                },
                "forecast": [
                    {
                        "date": "2024-01-02",
                        "daypower": "3",
                        "dayweather": "Cloudy",
                        "dayweather_code": 2,
                        "daywind": "E",
                        "maxtemp": "25",
                        "mintemp": "15",
                        "nightpower": "2",
                        "nightweatehr": "Clear",
                        "nightweatehr_code": 3,
                        "nightwind": "W",
                        "week": "Mon"
                    }
                ]
            }
        }`))
	})
	server := httptest.NewServer(handler)
	defer server.Close()

	weather := &Weather{
		conf:   Config{Host: server.URL, AppID: "appid", Secret: "secret", GameID: "game"},
		client: resty.New(),
	}

	result, err := weather.GetWeatherByLocation(context.Background(), 12.3456, 34.5678, "station-1", "adcode-1")
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}

	if result.Weather.Temperature != "20" {
		t.Fatalf("expected temperature 20, got %s", result.Weather.Temperature)
	}
	if len(result.Forecast) != 1 {
		t.Fatalf("expected 1 forecast entry, got %d", len(result.Forecast))
	}
	if result.Forecast[0].DayWeather != "Cloudy" {
		t.Fatalf("expected dayweather Cloudy, got %s", result.Forecast[0].DayWeather)
	}
}

func TestGetWeatherByLocationStatusCodeError(t *testing.T) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, _ *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	})
	server := httptest.NewServer(handler)
	defer server.Close()

	weather := &Weather{
		conf:   Config{Host: server.URL, AppID: "appid", Secret: "secret", GameID: "game"},
		client: resty.New(),
	}

	_, err := weather.GetWeatherByLocation(context.Background(), 0, 0, "", "")
	if err == nil {
		t.Fatalf("expected error for non-200 status code")
	}
	if !errors.Is(err, kit.ErrHTTPStatusCodeNotOK) {
		t.Fatalf("expected ErrHTTPStatusCodeNotOK, got %v", err)
	}
}

func TestGetWeatherByLocationBizError(t *testing.T) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, _ *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(`{"code": 1001}`))
	})
	server := httptest.NewServer(handler)
	defer server.Close()

	weather := &Weather{
		conf:   Config{Host: server.URL, AppID: "appid", Secret: "secret", GameID: "game"},
		client: resty.New(),
	}

	_, err := weather.GetWeatherByLocation(context.Background(), 0, 0, "", "")
	if err == nil {
		t.Fatalf("expected business error for non-zero code")
	}
	if !errors.Is(err, kit.ErrRequestBizCodeNotOK) {
		t.Fatalf("expected ErrRequestBizCodeNotOK, got %v", err)
	}
}
