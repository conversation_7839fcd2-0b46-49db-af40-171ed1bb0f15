GO ?= go
GOCACHE_DIR := $(CURDIR)/.cache
GOMODCACHE_DIR := $(CURDIR)/.gomodcache

.PHONY: test test-integration

# test runs unit tests with sandbox-friendly cache locations.
test:
	mkdir -p "$(GOCACHE_DIR)" "$(GOMODCACHE_DIR)"
	GOENV=off GOCACHE="$(GOCACHE_DIR)" GOMODCACHE="$(GOMODCACHE_DIR)" $(GO) test -v ./...

test-integration:
	mkdir -p "$(GOCACHE_DIR)" "$(GOMODCACHE_DIR)"
	GOENV=off GOCACHE="$(GOCACHE_DIR)" GOMODCACHE="$(GOMODCACHE_DIR)" $(GO) test -v -tags integration ./...
