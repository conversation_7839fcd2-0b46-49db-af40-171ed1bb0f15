package sdk_weather

import (
	"fmt"
	"strings"

	"ccc-gitlab.leihuo.netease.com/cccgo/config"
	"ccc-gitlab.leihuo.netease.com/cccgo/kit"
	"github.com/jinzhu/copier"
	"github.com/samber/do"
)

// Boot 预加载默认实例（必然），同时预加载指定实例列表。
// 参数：scopes：指定的预加载实例scope key列表。
// 返回：预加载错误时返回具体error。
// 注意：请务必在服务的Boot阶段而非其它阶段使用该函数。
func Boot(scopes ...string) func() error {
	return func() error {
		if err := provide(config.AppGroup()); err != nil {
			return fmt.Errorf("加载资源[%s]错误：%w", config.AppGroup(), err)
		}
		for _, scope := range scopes {
			if err := provide(scope); err != nil {
				return fmt.Errorf("加载资源[%s]错误：%w", scope, err)
			}
		}
		return nil
	}
}

// BootCustom 预加载指定实例列表，但不预加载默认实例。
// 参数：scopes：指定的预加载实例scope key列表。
// 返回：预加载错误时返回具体error。
// 注意：请务必在服务的Boot阶段而非其它阶段使用该函数。
func BootCustom(scopes ...string) func() error {
	return func() error {
		for _, scope := range scopes {
			if err := provide(scope); err != nil {
				return fmt.Errorf("加载资源[%s]错误：%w", scope, err)
			}
		}
		return nil
	}
}

// provide 提供指定scope实例
func provide(scope string) error {
	// 获取配置
	conf, err := getConf(scope)
	if err != nil {
		return err
	}

	// 初始化实例
	instance, err := New(conf)
	if err != nil {
		return err
	}

	// 挂载实例
	do.ProvideNamedValue(nil, iocPrefix+scope, instance)

	// 返回
	return nil
}

// getConf 获取配置
func getConf(scope string) (conf Config, err error) {
	// 先尝试核心配置
	core := config.Pick(configScope)
	if core.IsSet("map." + scope) {
		// 初始化默认配置
		conf, err = defaultConfig()
		if err != nil {
			return
		}

		// 融合{configScope}.toml[{map.scope}]
		key := core.GetString("map." + scope)
		if !core.IsSet(key) {
			return conf, fmt.Errorf("%w：配置%s.toml [%s=>%s]不存在", kit.ErrNotFound, configScope, scope, key)
		}
		err = core.UnmarshalKey(key, &conf)
		if err != nil {
			return conf, fmt.Errorf("%w：解析%s.toml [%s=>%s]错误；%w", kit.ErrDataUnmarshal, configScope, scope, key, err)
		}

		// 再融合app.toml[{bizConfigKey}]
		app := config.Pick()
		if app.IsSet(bizConfigKey) {
			err = app.UnmarshalKey(bizConfigKey, &conf)
			if err != nil {
				return conf, fmt.Errorf("%w：解析app.toml [%s]错误；%w", kit.ErrDataUnmarshal, bizConfigKey, err)
			}
		}

		// 返回核心配置
		return conf, nil
	}

	// 再尝试业务定制化配置app.toml[{scope}]
	customPrefix := "custom_" + configScope + "_"
	if len(scope) < len(customPrefix) || !strings.HasPrefix(scope, customPrefix) {
		return conf, fmt.Errorf("%w：配置%s.toml [%s]不存在", kit.ErrNotFound, configScope, scope)
	}
	app := config.Pick()
	if !app.IsSet(scope) {
		return conf, fmt.Errorf("%w：配置app.toml [%s]不存在", kit.ErrNotFound, scope)
	}
	conf, err = defaultConfig()
	if err != nil {
		return
	}
	err = app.UnmarshalKey(scope, &conf)
	if err != nil {
		return conf, fmt.Errorf("%w：解析app.toml [%s]错误；%w", kit.ErrDataUnmarshal, scope, err)
	}
	return conf, nil
}

// defaultConfig 获取默认配置
func defaultConfig() (conf Config, err error) {
	err = copier.CopyWithOption(&conf, &DefaultConfig, copier.Option{DeepCopy: true})
	return conf, err
}
