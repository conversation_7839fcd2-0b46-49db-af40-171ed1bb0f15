package sdk_weather

var DefaultConfig = Config{
	Logger:     "",
	HTTPClient: "",

	// 下面是组件定制化配置
	Host:   "",
	AppID:  "",
	Secret: "",
	GameID: "",
}

type Config struct {
	// 基础依赖组件实例配置（按需选用）
	Logger     string `mapstructure:"logger"`      // Logger 日志记录实例key
	HTTPClient string `mapstructure:"http_client"` // HTTPClient 使用的HTTP Client实例key

	// 业务相关配置
	Host   string `mapstructure:"host"`   // 天气服务地址
	AppID  string `mapstructure:"appid"`  // 实例ID(APPID)
	Secret string `mapstructure:"secret"` // 实例密钥(APPSecret)
	GameID string `mapstructure:"gameid"` // 默认游戏ID
}
