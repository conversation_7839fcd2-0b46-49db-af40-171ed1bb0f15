# sdk_weather

## 快速开始

### 安装

如果还未完成框架的前置工作，请先[参见此处](https://ccc-gitlab.leihuo.netease.com/cccgo/docs)完成脚手架配置。

```bash
ccc pkg sdk_weather
```

### 注册

```go
// ./register/boot.go

// SDK引导器----------------------------------------------
sdk_weather.Boot(),
```

如需在启动阶段按需加载多个实例，可结合 `sdk_weather.Boot("custom_scope")` 搭配配置文件中的 scope 使用。

### 配置

默认实例的配置维护在 `conf/sdk_weather.toml`：

```toml
[map]
$app.group$ = "weather_default"

[weather_default]
host = "https://example-weather.netease.com"
appid = "your-app-id"
secret = "your-app-secret"
gameid = "your-game-id"
```

若需要覆盖默认配置或提供定制实例，可在 `app.toml` 中声明：

```toml
[sdk_weather]
host = "https://weather.xxx"
appid = "appid"
secret = "secret"
gameid = "gameid"
```

`host`、`appid`、`secret`、`gameid` 均为必填；可选的依赖注入键 `logger`、`http_client` 若不设置会使用框架默认实例。

### 获取实例

```go
// 得到的 sdk 为 *sdk_weather.Weather 实例
weatherSDK := sdk_weather.Pick()

// 如需按 scope 获取定制实例，可传入 scope 名称：
// weatherSDK := sdk_weather.Pick("custom_scope")

result, err := weatherSDK.GetWeatherByLocation(ctx, 30.286659, 120.153576, "", "330100")
```

## 功能清单

- `GetWeatherByLocation(ctx, lat, log, station, adcode)`：根据经纬度或站号/城市编码查询天气。（`station`、`adcode` 可选；接口自动使用配置中的 `gameid`）
- `GetWeatherByStation(ctx, station)`：按气象站号获取当前天气及未来预报。
- `GetWeatherByAdcode(ctx, adcode)`：按行政区划编码获取当前天气及未来预报。

所有能力会基于配置生成 Basic 鉴权头，并对 HTTP 状态码和业务码做统一错误包装。

## 组件介绍

### 背景/场景

对接 XYQ-LBS 天气服务，提供当前天气及多日预报能力，适用于需在游戏或服务端按地区展示天气信息的场景。例如登录页展示当前城市天气、活动入口根据天气动态调整文案等。

## 基本概念

- `gameid`：与部署好的 XYQ-LBS 天气服务约定的业务标识，调用接口时必须保持一致。
- `station`/`adcode`：天气查询的可选标识，前者为气象站号，后者为行政区划编码。
- `WEATHER_SDK_DEBUG`：设置为 `true`/`1`/`on` 时，SDK 会输出调试日志（stdout 与注入的 logger）。
