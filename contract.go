package sdk_weather

import "errors"

var (
	ErrWeatherSDKInitErr = errors.New("weather sdk 初始化异常")
)

// WindDirection 风向编码枚举
type WindDirection int

const (
	WindDirectionNoWind    WindDirection = 0 // 无持续风向
	WindDirectionNortheast WindDirection = 1 // 东北风
	WindDirectionEast      WindDirection = 2 // 东风
	WindDirectionSoutheast WindDirection = 3 // 东南风
	WindDirectionSouth     WindDirection = 4 // 南风
	WindDirectionSouthwest WindDirection = 5 // 西南风
	WindDirectionWest      WindDirection = 6 // 西风
	WindDirectionNorthwest WindDirection = 7 // 西北风
	WindDirectionNorth     WindDirection = 8 // 北风
	WindDirectionWhirl     WindDirection = 9 // 旋转风
)

// String 返回风向的中文名称
func (wd WindDirection) String() string {
	switch wd {
	case WindDirectionNoWind:
		return "无持续风向"
	case WindDirectionNortheast:
		return "东北风"
	case WindDirectionEast:
		return "东风"
	case WindDirectionSoutheast:
		return "东南风"
	case WindDirectionSouth:
		return "南风"
	case WindDirectionSouthwest:
		return "西南风"
	case WindDirectionWest:
		return "西风"
	case WindDirectionNorthwest:
		return "西北风"
	case WindDirectionNorth:
		return "北风"
	case WindDirectionWhirl:
		return "旋转风"
	default:
		return "未知"
	}
}

// EnglishName 返回风向的英文名称
func (wd WindDirection) EnglishName() string {
	switch wd {
	case WindDirectionNoWind:
		return "No wind"
	case WindDirectionNortheast:
		return "Northeast"
	case WindDirectionEast:
		return "East"
	case WindDirectionSoutheast:
		return "Southeast"
	case WindDirectionSouth:
		return "South"
	case WindDirectionSouthwest:
		return "Southwest"
	case WindDirectionWest:
		return "West"
	case WindDirectionNorthwest:
		return "Northwest"
	case WindDirectionNorth:
		return "North"
	case WindDirectionWhirl:
		return "Whirl wind"
	default:
		return "Unknown"
	}
}

// WindPower 风力编码枚举
type WindPower int

const (
	WindPowerGentle      WindPower = 0 // 微风 <5.4m/s
	WindPowerLevel3To4   WindPower = 1 // 3-4级 5.5~7.9m/s
	WindPowerLevel4To5   WindPower = 2 // 4-5级 8.0~10.7m/s
	WindPowerLevel5To6   WindPower = 3 // 5-6级 10.8~13.8m/s
	WindPowerLevel6To7   WindPower = 4 // 6-7级 13.9~17.1m/s
	WindPowerLevel7To8   WindPower = 5 // 7-8级 17.2~20.7m/s
	WindPowerLevel8To9   WindPower = 6 // 8-9级 20.8~24.4m/s
	WindPowerLevel9To10  WindPower = 7 // 9-10级 24.5~28.4m/s
	WindPowerLevel10To11 WindPower = 8 // 10-11级 28.5~32.6m/s
	WindPowerLevel11To12 WindPower = 9 // 11-12级 32.7~36.9m/s
)

// String 返回风力的中文名称
func (wp WindPower) String() string {
	switch wp {
	case WindPowerGentle:
		return "微风"
	case WindPowerLevel3To4:
		return "3-4级"
	case WindPowerLevel4To5:
		return "4-5级"
	case WindPowerLevel5To6:
		return "5-6级"
	case WindPowerLevel6To7:
		return "6-7级"
	case WindPowerLevel7To8:
		return "7-8级"
	case WindPowerLevel8To9:
		return "8-9级"
	case WindPowerLevel9To10:
		return "9-10级"
	case WindPowerLevel10To11:
		return "10-11级"
	case WindPowerLevel11To12:
		return "11-12级"
	default:
		return "未知"
	}
}

// EnglishName 返回风力的英文名称（风速范围）
func (wp WindPower) EnglishName() string {
	switch wp {
	case WindPowerGentle:
		return "<5.4m/s"
	case WindPowerLevel3To4:
		return "5.5~7.9m/s"
	case WindPowerLevel4To5:
		return "8.0~10.7m/s"
	case WindPowerLevel5To6:
		return "10.8~13.8m/s"
	case WindPowerLevel6To7:
		return "13.9~17.1m/s"
	case WindPowerLevel7To8:
		return "17.2~20.7m/s"
	case WindPowerLevel8To9:
		return "20.8~24.4m/s"
	case WindPowerLevel9To10:
		return "24.5~28.4m/s"
	case WindPowerLevel10To11:
		return "28.5~32.6m/s"
	case WindPowerLevel11To12:
		return "32.7~36.9m/s"
	default:
		return "Unknown"
	}
}

// WeatherPhenomenon 天气现象编码枚举
type WeatherPhenomenon int

const (
	WeatherSunny                 WeatherPhenomenon = 0   // 晴
	WeatherCloudy                WeatherPhenomenon = 1   // 多云
	WeatherOvercast              WeatherPhenomenon = 2   // 阴
	WeatherShower                WeatherPhenomenon = 3   // 阵雨
	WeatherThundershower         WeatherPhenomenon = 4   // 雷阵雨
	WeatherThundershowerWithHail WeatherPhenomenon = 5   // 雷阵雨伴有冰雹
	WeatherSleet                 WeatherPhenomenon = 6   // 雨夹雪
	WeatherLightRain             WeatherPhenomenon = 7   // 小雨
	WeatherModerateRain          WeatherPhenomenon = 8   // 中雨
	WeatherHeavyRain             WeatherPhenomenon = 9   // 大雨
	WeatherStorm                 WeatherPhenomenon = 10  // 暴雨
	WeatherHeavyStorm            WeatherPhenomenon = 11  // 大暴雨
	WeatherSevereStorm           WeatherPhenomenon = 12  // 特大暴雨
	WeatherSnowFlurry            WeatherPhenomenon = 13  // 阵雪
	WeatherLightSnow             WeatherPhenomenon = 14  // 小雪
	WeatherModerateSnow          WeatherPhenomenon = 15  // 中雪
	WeatherHeavySnow             WeatherPhenomenon = 16  // 大雪
	WeatherSnowstorm             WeatherPhenomenon = 17  // 暴雪
	WeatherFoggy                 WeatherPhenomenon = 18  // 雾
	WeatherIceRain               WeatherPhenomenon = 19  // 冻雨
	WeatherDuststorm             WeatherPhenomenon = 20  // 沙尘暴
	WeatherLightToModerateRain   WeatherPhenomenon = 21  // 小到中雨
	WeatherModerateToHeavyRain   WeatherPhenomenon = 22  // 中到大雨
	WeatherHeavyRainToStorm      WeatherPhenomenon = 23  // 大到暴雨
	WeatherStormToHeavyStorm     WeatherPhenomenon = 24  // 暴雨到大暴雨
	WeatherHeavyToSevereStorm    WeatherPhenomenon = 25  // 大暴雨到特大暴雨
	WeatherLightToModerateSnow   WeatherPhenomenon = 26  // 小到中雪
	WeatherModerateToHeavySnow   WeatherPhenomenon = 27  // 中到大雪
	WeatherHeavySnowToSnowstorm  WeatherPhenomenon = 28  // 大到暴雪
	WeatherDust                  WeatherPhenomenon = 29  // 浮尘
	WeatherSand                  WeatherPhenomenon = 30  // 扬沙
	WeatherSandstorm             WeatherPhenomenon = 31  // 强沙尘暴
	WeatherDenseFog              WeatherPhenomenon = 32  // 浓雾
	WeatherStrongFog             WeatherPhenomenon = 49  // 强浓雾
	WeatherHaze                  WeatherPhenomenon = 53  // 霾
	WeatherModerateHaze          WeatherPhenomenon = 54  // 中度霾
	WeatherSevereHaze            WeatherPhenomenon = 55  // 重度霾
	WeatherSeriousHaze           WeatherPhenomenon = 56  // 严重霾
	WeatherDenseFog2             WeatherPhenomenon = 57  // 大雾
	WeatherExtraHeavyFog         WeatherPhenomenon = 58  // 特强浓雾
	WeatherUnknown               WeatherPhenomenon = 99  // 无
	WeatherRain                  WeatherPhenomenon = 301 // 雨
	WeatherSnow                  WeatherPhenomenon = 302 // 雪
)

// String 返回天气现象的中文名称
func (wp WeatherPhenomenon) String() string {
	switch wp {
	case WeatherSunny:
		return "晴"
	case WeatherCloudy:
		return "多云"
	case WeatherOvercast:
		return "阴"
	case WeatherShower:
		return "阵雨"
	case WeatherThundershower:
		return "雷阵雨"
	case WeatherThundershowerWithHail:
		return "雷阵雨伴有冰雹"
	case WeatherSleet:
		return "雨夹雪"
	case WeatherLightRain:
		return "小雨"
	case WeatherModerateRain:
		return "中雨"
	case WeatherHeavyRain:
		return "大雨"
	case WeatherStorm:
		return "暴雨"
	case WeatherHeavyStorm:
		return "大暴雨"
	case WeatherSevereStorm:
		return "特大暴雨"
	case WeatherSnowFlurry:
		return "阵雪"
	case WeatherLightSnow:
		return "小雪"
	case WeatherModerateSnow:
		return "中雪"
	case WeatherHeavySnow:
		return "大雪"
	case WeatherSnowstorm:
		return "暴雪"
	case WeatherFoggy:
		return "雾"
	case WeatherIceRain:
		return "冻雨"
	case WeatherDuststorm:
		return "沙尘暴"
	case WeatherLightToModerateRain:
		return "小到中雨"
	case WeatherModerateToHeavyRain:
		return "中到大雨"
	case WeatherHeavyRainToStorm:
		return "大到暴雨"
	case WeatherStormToHeavyStorm:
		return "暴雨到大暴雨"
	case WeatherHeavyToSevereStorm:
		return "大暴雨到特大暴雨"
	case WeatherLightToModerateSnow:
		return "小到中雪"
	case WeatherModerateToHeavySnow:
		return "中到大雪"
	case WeatherHeavySnowToSnowstorm:
		return "大到暴雪"
	case WeatherDust:
		return "浮尘"
	case WeatherSand:
		return "扬沙"
	case WeatherSandstorm:
		return "强沙尘暴"
	case WeatherDenseFog:
		return "浓雾"
	case WeatherStrongFog:
		return "强浓雾"
	case WeatherHaze:
		return "霾"
	case WeatherModerateHaze:
		return "中度霾"
	case WeatherSevereHaze:
		return "重度霾"
	case WeatherSeriousHaze:
		return "严重霾"
	case WeatherDenseFog2:
		return "大雾"
	case WeatherExtraHeavyFog:
		return "特强浓雾"
	case WeatherUnknown:
		return "无"
	case WeatherRain:
		return "雨"
	case WeatherSnow:
		return "雪"
	default:
		return "未知"
	}
}

// EnglishName 返回天气现象的英文名称
func (wp WeatherPhenomenon) EnglishName() string {
	switch wp {
	case WeatherSunny:
		return "Sunny"
	case WeatherCloudy:
		return "Cloudy"
	case WeatherOvercast:
		return "Overcast"
	case WeatherShower:
		return "Shower"
	case WeatherThundershower:
		return "Thundershower"
	case WeatherThundershowerWithHail:
		return "Thundershower with hail"
	case WeatherSleet:
		return "Sleet"
	case WeatherLightRain:
		return "Light rain"
	case WeatherModerateRain:
		return "Moderate rain"
	case WeatherHeavyRain:
		return "Heavy rain"
	case WeatherStorm:
		return "Storm"
	case WeatherHeavyStorm:
		return "Heavy storm"
	case WeatherSevereStorm:
		return "Severe storm"
	case WeatherSnowFlurry:
		return "Snow flurry"
	case WeatherLightSnow:
		return "Light snow"
	case WeatherModerateSnow:
		return "Moderate snow"
	case WeatherHeavySnow:
		return "Heavy snow"
	case WeatherSnowstorm:
		return "Snowstorm"
	case WeatherFoggy:
		return "Foggy"
	case WeatherIceRain:
		return "Ice rain"
	case WeatherDuststorm:
		return "Duststorm"
	case WeatherLightToModerateRain:
		return "Light to moderate rain"
	case WeatherModerateToHeavyRain:
		return "Moderate to heavy rain"
	case WeatherHeavyRainToStorm:
		return "Heavy rain to storm"
	case WeatherStormToHeavyStorm:
		return "Storm to heavy storm"
	case WeatherHeavyToSevereStorm:
		return "Heavy to severe storm"
	case WeatherLightToModerateSnow:
		return "Light to moderate snow"
	case WeatherModerateToHeavySnow:
		return "Moderate to heavy snow"
	case WeatherHeavySnowToSnowstorm:
		return "Heavy snow to snowstorm"
	case WeatherDust:
		return "Dust"
	case WeatherSand:
		return "Sand"
	case WeatherSandstorm:
		return "Sandstorm"
	case WeatherDenseFog:
		return "Dense fog"
	case WeatherStrongFog:
		return "Strong fog"
	case WeatherHaze:
		return "Haze"
	case WeatherModerateHaze:
		return "Moderate haze"
	case WeatherSevereHaze:
		return "Severe haze"
	case WeatherSeriousHaze:
		return "Severe haze"
	case WeatherDenseFog2:
		return "Dense fog"
	case WeatherExtraHeavyFog:
		return "Extra heavy fog"
	case WeatherUnknown:
		return "Unknown"
	case WeatherRain:
		return "rain"
	case WeatherSnow:
		return "snow"
	default:
		return "Unknown"
	}
}

// WeatherInfo 当前天气信息
type WeatherInfo struct {
	Humidity      string            `json:"humidity" desc:"湿度"`
	ReportTime    string            `json:"reporttime" desc:"上报时间"`
	Temperature   string            `json:"temperature" desc:"当前摄氏温度"`
	Weather       string            `json:"weather" desc:"当前天气现象"`
	WeatherCode   WeatherPhenomenon `json:"weather_code" desc:"当前天气现象编码"`
	WindDirection WindDirection     `json:"winddirection" desc:"风向"`
	WindPower     WindPower         `json:"windpower" desc:"风力"`
	// 保留原始字符串字段用于向后兼容
	WeatherStr       string `json:"weather_str,omitempty" desc:"天气现象字符串"`
	WindDirectionStr string `json:"winddirection_str,omitempty" desc:"风向字符串"`
	WindPowerStr     string `json:"windpower_str,omitempty" desc:"风力字符串"`
}

// ForecastInfo 天气预报信息
type ForecastInfo struct {
	Date             string            `json:"date" desc:"预报日期"`
	DayPower         WindPower         `json:"daypower" desc:"白天风力"`
	DayWeather       string            `json:"dayweather" desc:"白天天气"`
	DayWeatherCode   WeatherPhenomenon `json:"dayweather_code" desc:"白天天气现象编码"`
	DayWind          WindDirection     `json:"daywind" desc:"白天风向"`
	MaxTemp          string            `json:"maxtemp" desc:"最高温度"`
	MinTemp          string            `json:"mintemp" desc:"最低温度"`
	NightPower       WindPower         `json:"nightpower" desc:"晚上风力"`
	NightWeather     string            `json:"nightweatehr" desc:"晚上天气"`
	NightWeatherCode WeatherPhenomenon `json:"nightweatehr_code" desc:"晚上天气现象编码"`
	NightWind        WindDirection     `json:"nightwind" desc:"晚上风向"`
	Week             string            `json:"week" desc:"周几"`
	// 保留原始字符串字段用于向后兼容
	DayPowerStr   string `json:"daypower_str,omitempty" desc:"白天风力字符串"`
	DayWindStr    string `json:"daywind_str,omitempty" desc:"白天风向字符串"`
	NightPowerStr string `json:"nightpower_str,omitempty" desc:"晚上风力字符串"`
	NightWindStr  string `json:"nightwind_str,omitempty" desc:"晚上风向字符串"`
}

// WeatherResult 天气查询结果
type WeatherResult struct {
	Weather  WeatherInfo    `json:"weather" desc:"当前天气信息"`
	Forecast []ForecastInfo `json:"forecast" desc:"天气预报"`
}

// WeatherResponse 天气API响应
type WeatherResponse struct {
	Code   int           `json:"code" desc:"返回结果状态值"`
	Result WeatherResult `json:"result" desc:"天气信息结果"`
}

// WeatherRequest 天气查询请求参数
type WeatherRequest struct {
	GameID  string  `json:"gameid" desc:"游戏id"`
	Lat     float64 `json:"lat" desc:"纬度"`
	Log     float64 `json:"log" desc:"经度"`
	Station string  `json:"station,omitempty" desc:"站号"`
	Adcode  string  `json:"adcode,omitempty" desc:"城市编码"`
}
