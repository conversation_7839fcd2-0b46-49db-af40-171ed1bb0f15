package sdk_weather

import "errors"

var (
	ErrWeatherSDKInitErr = errors.New("weather sdk 初始化异常")
)

// WeatherInfo 当前天气信息
type WeatherInfo struct {
	Humidity      string `json:"humidity" desc:"湿度"`
	ReportTime    string `json:"reporttime" desc:"上报时间"`
	Temperature   string `json:"temperature" desc:"当前摄氏温度"`
	Weather       string `json:"weather" desc:"当前天气现象"`
	WeatherCode   int    `json:"weather_code" desc:"当前天气现象编码"`
	WindDirection string `json:"winddirection" desc:"风向"`
	WindPower     string `json:"windpower" desc:"风力"`
}

// ForecastInfo 天气预报信息
type ForecastInfo struct {
	Date             string `json:"date" desc:"预报日期"`
	DayPower         string `json:"daypower" desc:"白天风力"`
	DayWeather       string `json:"dayweather" desc:"白天天气"`
	DayWeatherCode   int    `json:"dayweather_code" desc:"白天天气现象编码"`
	DayWind          string `json:"daywind" desc:"白天风向"`
	MaxTemp          string `json:"maxtemp" desc:"最高温度"`
	MinTemp          string `json:"mintemp" desc:"最低温度"`
	NightPower       string `json:"nightpower" desc:"晚上风力"`
	NightWeather     string `json:"nightweatehr" desc:"晚上天气"`
	NightWeatherCode int    `json:"nightweatehr_code" desc:"晚上天气现象编码"`
	NightWind        string `json:"nightwind" desc:"晚上风向"`
	Week             string `json:"week" desc:"周几"`
}

// WeatherResult 天气查询结果
type WeatherResult struct {
	Weather  WeatherInfo    `json:"weather" desc:"当前天气信息"`
	Forecast []ForecastInfo `json:"forecast" desc:"天气预报"`
}

// WeatherResponse 天气API响应
type WeatherResponse struct {
	Code   int           `json:"code" desc:"返回结果状态值"`
	Result WeatherResult `json:"result" desc:"天气信息结果"`
}

// WeatherRequest 天气查询请求参数
type WeatherRequest struct {
	GameID  string  `json:"gameid" desc:"游戏id"`
	Lat     float64 `json:"lat" desc:"纬度"`
	Log     float64 `json:"log" desc:"经度"`
	Station string  `json:"station,omitempty" desc:"站号"`
	Adcode  string  `json:"adcode,omitempty" desc:"城市编码"`
}
