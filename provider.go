package sdk_weather

import (
	"ccc-gitlab.leihuo.netease.com/cccgo/config"
	"github.com/samber/do"
)

const (
	iocPrefix    = "_sdk_weather_:"
	configScope  = "sdk_weather"
	bizConfigKey = "sdk_weather"
)

// Exist 判断scope实例是否挂载（被Boot过）且类型正确。
// 参数：scope：具体判断的scope key。
// 返回：已挂载（被Boot过）且类型正确返回true，否则返回false。
func Exist(scope string) bool {
	_, err := do.InvokeNamed[*Weather](nil, iocPrefix+scope)
	return err == nil
}

// Pick 获取指定scope实例。
//
// 参数：
// scopes：可选的scope key，不指定时则自动指定默认scope。
//
// 返回：
// 当对应scope实例挂载（被Boot过）且类型正确时，返回具体实例。
// 否则将直接panic出具体error。
//
// 注意：
// 请在 Boot 函数执行后执行 Pick。
// Boot()之后可以执行 Pick()，但不能执行 Pick("a")。
// Boot("a")之后可以执行 Pick()、 Pick("a")，但不能执行 Pick("b")。
func Pick(scopes ...string) *Weather {
	var scope string
	if len(scopes) == 0 || scopes[0] == "" {
		scope = config.AppGroup()
	} else {
		scope = scopes[0]
	}
	return do.MustInvokeNamed[*Weather](nil, iocPrefix+scope)
}
