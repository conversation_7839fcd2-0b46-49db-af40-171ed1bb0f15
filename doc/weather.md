# 天气服务 API 文档

本文档整理自, 最新信息建议浏览上游文档[NgLBS服务-天气接口](https://unisdk.nie.netease.com/doc/page/socialmatrix/zh/service-scenario/socialandfriends/lbs#get-v1weather-%E5%A4%A9%E6%B0%94%E4%BF%A1%E6%81%AF)


## 目录
- [认证方式](#认证方式)
- [天气信息接口](#天气信息接口)
- [参数说明](#参数说明)
- [返回结果](#返回结果)
- [编码表](#编码表)
- [参考资料](#参考资料)

## 认证方式

### Basic Authorization 鉴权

游戏服从游戏服入口（Mesh网关 IDC）访问LBS时，需要经过Basic Authorization认证。

#### 获取认证信息

1. 登录SocialMatrix管理后台
2. 进入：实例详情 → 模块配置 → 基础模块 → SocialMesh
3. 获取实例ID(APPID)和实例密钥(APPSecret)

#### 认证方式

**方式一：URL中直接添加认证信息**
```
http://<实例id>:<实例密钥>@具体的地址
```

**方式二：在请求头中添加Authorization**

```python
import requests
import base64

# 认证信息
app_id = "你的实例ID(APPID)"
app_secret = "你的实例密钥(APPSecret)"
basic_auth_token = "Basic %s" % (base64.b64encode(bytes("%s:%s" % (app_id, app_secret), encoding="ascii")).decode("ascii"),)

# 请求配置
channel = "channelA"
url = "http://127.0.0.1:18876/xyq_service/admin/xyq-lbs"  # 游戏服入口（Mesh网关 IDC）
path = "/v1/neighbor"
gameid = "你的项目代号"

# 查询参数（isinsert和mongo为false表示不插入该用户的位置数据）
query_string = "?gameid=" + gameid + "&serverid=" + channel + "distance=1000&period=24&limit=10&cursor=0&isinsert=false&mongo=false"

# 请求体
payload = {
    "user": {
        "uid": "test_uid_3",
        "udid": "test_udid_3",
        "roleid": "test_roleid_3"
    },
    "system": {
        "platform": "android"
    },
    "location": {
        "gaode": {
            "lat": 23.1,
            "log": 113.3
        }
    },
    "sortby": "dist"
}

# 请求头
headers = {
    'Authorization': basic_auth_token,
    'Content-Type': 'application/json'
}

# 发送请求
response = requests.request("POST", url + path + query_string, headers=headers, json=payload)
print(response.text)
```

---

## 天气信息接口

### GET /v1/weather
**版本要求：** NgLBS 0.20.0或以上版本支持（请加群 1409901 确认版本）

**功能描述：** 提供从坐标获取目标位置的天气信息，返回天气信息以县为单位

### 接口信息

| 项目 | 值 |
|------|-----|
| HTTP方法 | GET |
| URI | /v1/weather |
| GRPC类型 | Unary |
| GRPC方法 | v1.weather.get |

## 参数说明

### 请求参数 (model.WeatherGetReq)

| 参数 | 类型 | 必传 | 参数检验 | 描述 |
|------|------|------|----------|------|
| gameid | string | ✓ | required | 游戏id。需要和部署的xyq-lbs服务配置的gameid(若有)相同。例如：g0<br/>*HTTP请求时需要放在query string* |
| lat | float64 | ✓ | required | 纬度，必须参数。例如：23.1<br/>*HTTP请求时需要放在query string* |
| log | float64 | ✓ | required | 经度，必须参数。例如：113.3<br/>*HTTP请求时需要放在query string* |
| station | string | - | - | 站号。示例：101280109<br/>*参考：[气象站号](#气象站号)*<br/>*HTTP请求时需要放在query string* |
| adcode | string | - | - | 城市编码。示例：440113<br/>可以是/v1/regeo结果中的adcode，也可以在[国内城市地理位置和站号、adcode的映射表](#国内城市地理位置和站号adcode的映射表)中查询<br/>*HTTP请求时需要放在query string* |

**补充说明：** 可以使用 `lat,log` / `station` / `adcode` 来查询，三选一即可

## 返回结果

### 响应参数说明

| 名称 | 参数含义 | 规则说明 | 示例 |
|------|----------|----------|------|
| code | 返回结果状态值 | 参考状态码表 | 0 |
| result | 天气信息结果 | - | - |
| └─ weather | 当前天气信息 | - | - |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ weather_code | 当前天气现象编码 | 参考天气现象编码表 | - |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ humidity | 湿度 | - | 62 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ reporttime | 上报时间 | - | 2018-10-15 16:30:00 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ temperature | 当前摄氏温度 | - | 31 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ winddirection | 风向 | - | 北风 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ windpower | 风力 | - | 1 |
| └─ forecast | 天气预报 | 数组 | - |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ date | 预报日期 | - | 2018-10-16 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ daypower | 白天风力 | - | 0 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ dayweather | 白天天气 | - | 多云 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ dayweather_code | 白天天气现象编码 | 参考天气现象编码表 | - |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ daywind | 白天风向 | - | 无持续风向 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ maxtemp | 最高温度 | - | 34 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ mintemp | 最低温度 | - | 26 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ nightpower | 晚上风力 | - | 0 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ nightweatehr | 晚上天气 | - | 多云 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ nightweatehr_code | 晚上天气现象编码 | 参考天气现象编码表 | - |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ nightwind | 晚上风向 | - | 无持续风向 |
| &nbsp;&nbsp;&nbsp;&nbsp;└─ week | 周几 | - | Tuesday |

### 状态码说明

| code | 含义 |
|------|------|
| 0 | 正确返回 |
| 1 | 服务器内部错误 |
| 2 | 方圆20公里以内没有任何已知位置 |
| 3 | 敏感地区 |
| 4 | 服务器内部错误 |
### 响应示例

**请求示例：** 查询经度113.335365，纬度23.13559的地理位置天气信息

**响应结果：**
```json
{
    "code": 0,
    "result": {
        "weather": {
            "humidity": "76",
            "reporttime": "2018-11-20 16:25:00",
            "temperature": "21",
            "weather": "多云",
            "weather_code": 1,
            "winddirection": "东风",
            "windpower": "1"
        },
        "forecast": [
            {
                "date": "2018-11-20",
                "daypower": "0",
                "dayweather": "多云",
                "dayweather_code": 1,
                "daywind": "无持续风向",
                "maxtemp": "24",
                "mintemp": "17",
                "nightpower": "0",
                "nightweatehr": "多云",
                "nightweather_code": 1,
                "nightwind": "无持续风向",
                "week": "Tuesday"
            },
            {
                "date": "2018-11-21",
                "daypower": "8",
                "dayweather": "小到中雨",
                "dayweather_code": 21,
                "daywind": "东北风",
                "maxtemp": "25",
                "mintemp": "14",
                "nightpower": "8",
                "nightweatehr": "中雨",
                "nightweather_code": 8,
                "nightwind": "东风",
                "week": "Wednesday"
            },
            {
                "date": "2018-11-22",
                "daypower": "8",
                "dayweather": "中雨",
                "dayweather_code": 8,
                "daywind": "东北风",
                "maxtemp": "19",
                "mintemp": "13",
                "nightpower": "8",
                "nightweatehr": "多云",
                "nightweather_code": 1,
                "nightwind": "东北风",
                "week": "Thursday"
            },
            {
                "date": "2018-11-23",
                "daypower": "0",
                "dayweather": "晴",
                "dayweather_code": 0,
                "daywind": "无持续风向",
                "maxtemp": "23",
                "mintemp": "13",
                "nightpower": "0",
                "nightweatehr": "多云",
                "nightweather_code": 1,
                "nightwind": "无持续风向",
                "week": "Friday"
            }
        ]
    }
}
```

---
## 编码表

### 风力风向编码表

#### 风向编码

| 编号 | 中文名称 | 英文名称 |
|------|----------|----------|
| 0 | 无持续风向 | No wind |
| 1 | 东北风 | Northeast |
| 2 | 东风 | East |
| 3 | 东南风 | Southeast |
| 4 | 南风 | South |
| 5 | 西南风 | Southwest |
| 6 | 西风 | West |
| 7 | 西北风 | Northwest |
| 8 | 北风 | North |
| 9 | 旋转风 | Whirl wind |

#### 风力编码

| 编号 | 中文名称 | 英文名称 |
|------|----------|----------|
| 0 | 微风 | <5.4m/s |
| 1 | 3-4级 | 5.5~7.9m/s |
| 2 | 4-5级 | 8.0~10.7m/s |
| 3 | 5-6级 | 10.8~13.8m/s |
| 4 | 6-7级 | 13.9~17.1m/s |
| 5 | 7-8级 | 17.2~20.7m/s |
| 6 | 8-9级 | 20.8~24.4m/s |
| 7 | 9-10级 | 24.5~28.4m/s |
| 8 | 10-11级 | 28.5~32.6m/s |
| 9 | 11-12级 | 32.7~36.9m/s |
### 天气现象编码表

| 编号 | 中文名称 | 英文名称 |
|------|----------|----------|
| 00 | 晴 | Sunny |
| 01 | 多云 | Cloudy |
| 02 | 阴 | Overcast |
| 03 | 阵雨 | Shower |
| 04 | 雷阵雨 | Thundershower |
| 05 | 雷阵雨伴有冰雹 | Thundershower with hail |
| 06 | 雨夹雪 | Sleet |
| 07 | 小雨 | Light rain |
| 08 | 中雨 | Moderate rain |
| 09 | 大雨 | Heavy rain |
| 10 | 暴雨 | Storm |
| 11 | 大暴雨 | Heavy storm |
| 12 | 特大暴雨 | Severe storm |
| 13 | 阵雪 | Snow flurry |
| 14 | 小雪 | Light snow |
| 15 | 中雪 | Moderate snow |
| 16 | 大雪 | Heavy snow |
| 17 | 暴雪 | Snowstorm |
| 18 | 雾 | Foggy |
| 19 | 冻雨 | Ice rain |
| 20 | 沙尘暴 | Duststorm |
| 21 | 小到中雨 | Light to moderate rain |
| 22 | 中到大雨 | Moderate to heavy rain |
| 23 | 大到暴雨 | Heavy rain to storm |
| 24 | 暴雨到大暴雨 | Storm to heavy storm |
| 25 | 大暴雨到特大暴雨 | Heavy to severe storm |
| 26 | 小到中雪 | Light to moderate snow |
| 27 | 中到大雪 | Moderate to heavy snow |
| 28 | 大到暴雪 | Heavy snow to snowstorm |
| 29 | 浮尘 | Dust |
| 30 | 扬沙 | Sand |
| 31 | 强沙尘暴 | Sandstorm |
| 32 | 浓雾 | Dense fog |
| 49 | 强浓雾 | Strong fog |
| 53 | 霾 | Haze |
| 54 | 中度霾 | Moderate haze |
| 55 | 重度霾 | Severe haze |
| 56 | 严重霾 | Severe haze |
| 57 | 大雾 | Dense fog |
| 58 | 特强浓雾 | Extra heavy fog |
| 99 | 无 | Unknown |
| 301 | 雨 | rain |
| 302 | 雪 | snow |

---