//go:build integration

package sdk_weather

import (
	"context"
	"errors"
	"os"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func TestWeatherIntegration_GetWeatherByLocation(t *testing.T) {
	loadIntegrationEnv(t)

	host := requireEnv(t, "WEATHER_HOST")
	appID := requireEnv(t, "WEATHER_APPID")
	secret := requireEnv(t, "WEATHER_SECRET")
	gameID := requireEnv(t, "WEATHER_GAMEID")

	cfg := Config{Host: host, AppID: appID, Secret: secret, GameID: gameID}

	client := resty.New()
	client.SetTimeout(10 * time.Second)
	client.SetHeader("User-Agent", "sdk-weather-integration-test")

	weather := &Weather{
		conf:   cfg,
		logger: logrus.New(),
		client: client,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := weather.GetWeatherByLocation(ctx, 23.13559, 113.335365, "", "")
	if err != nil {
		t.Fatalf("integration request failed: %v", err)
	}

	if result == nil {
		t.Fatalf("expected weather result, got nil")
	}

	if result.Weather.Weather == "" && result.Weather.Temperature == "" {
		t.Fatalf("unexpected empty weather payload: %+v", result.Weather)
	}
}

var (
	envOnce sync.Once
	envErr  error
	envCfg  *viper.Viper
)

func loadIntegrationEnv(t *testing.T) {
	t.Helper()
	envOnce.Do(func() {
		v := viper.New()
		v.SetConfigFile(".env")
		v.SetConfigType("env")
		v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
		v.AutomaticEnv()
		if err := v.ReadInConfig(); err != nil {
			var notFound viper.ConfigFileNotFoundError
			if errors.As(err, &notFound) {
				return
			}
			envErr = err
			return
		}
		envCfg = v
	})
	if envErr != nil {
		t.Fatalf("load .env failed: %v", envErr)
	}
}

func requireEnv(t *testing.T, key string) string {
	t.Helper()
	value := strings.TrimSpace(os.Getenv(key))
	if value == "" && envCfg != nil {
		value = strings.TrimSpace(envCfg.GetString(key))
		if value == "" {
			value = strings.TrimSpace(envCfg.GetString(strings.ToLower(key)))
		}
	}
	if value == "" {
		t.Skipf("missing %s; populate environment or .env to run integration test", key)
	}
	return value
}
